"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Upload, FileText, AlertCircle } from "lucide-react";
import { toast } from "sonner";

export default function AddTemplatePage() {
  const router = useRouter();
  const [isUploading, setIsUploading] = useState(false);
  const [isConverting, setIsConverting] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    layoutSize: "A4" as "A4" | "Letter",
    file: null as File | null,
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file type
      const fileName = file.name.toLowerCase();
      if (
        !fileName.endsWith(".html") &&
        !fileName.endsWith(".htm") &&
        !fileName.endsWith(".docx")
      ) {
        toast.error(
          "Please select an HTML file (.html, .htm) or DOCX file (.docx)"
        );
        return;
      }

      setFormData((prev) => ({ ...prev, file }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error("Please enter a template name");
      return;
    }

    if (!formData.description.trim()) {
      toast.error("Please enter a template description");
      return;
    }

    if (!formData.file) {
      toast.error("Please select an HTML or DOCX file");
      return;
    }

    setIsUploading(true);

    try {
      let htmlContent: string;

      // Check if file is DOCX and needs conversion
      if (formData.file.name.toLowerCase().endsWith(".docx")) {
        setIsConverting(true);
        toast.info("Converting DOCX to HTML...");

        // Convert DOCX to HTML using our API
        const convertFormData = new FormData();
        convertFormData.append("file", formData.file);

        const convertResponse = await fetch("/api/templates/convert-docx", {
          method: "POST",
          body: convertFormData,
        });

        if (!convertResponse.ok) {
          const error = await convertResponse.json();
          throw new Error(error.message || "Failed to convert DOCX file");
        }

        const convertResult = await convertResponse.json();
        htmlContent = convertResult.htmlContent;
        setIsConverting(false);
        toast.success("DOCX converted to HTML successfully!");
      } else {
        // For HTML files, read content directly
        htmlContent = await formData.file.text();
      }

      // Now create the template with the HTML content
      const response = await fetch("/api/templates", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: formData.name.trim(),
          description: formData.description.trim(),
          layoutSize: formData.layoutSize,
          htmlContent: htmlContent,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to upload template");
      }

      await response.json();
      toast.success("Template uploaded successfully!");
      router.push("/");
    } catch (error) {
      console.error("Upload error:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to upload template"
      );
    } finally {
      setIsUploading(false);
      setIsConverting(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      <div className="mb-8">
        <h1 className="text-3xl font-semibold text-foreground mb-2">
          Add New Template
        </h1>
        <p className="text-muted-foreground">
          Upload a DOCX file or HTML template file (saved as &quot;Web Page,
          Filtered&quot; from Word) to create a new document template.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Template Information
          </CardTitle>
          <CardDescription>
            Provide details about your template and upload the DOCX or HTML
            file.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="name">Template Name</Label>
              <Input
                id="name"
                type="text"
                placeholder="e.g., Certificate of Good Moral"
                value={formData.name}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, name: e.target.value }))
                }
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Input
                id="description"
                type="text"
                placeholder="e.g., Certificate of good moral character"
                value={formData.description}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    description: e.target.value,
                  }))
                }
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="layoutSize">Layout Size</Label>
              <Select
                value={formData.layoutSize}
                onValueChange={(value: "A4" | "Letter") =>
                  setFormData((prev) => ({ ...prev, layoutSize: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select layout size" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="A4">A4 (210 × 297 mm)</SelectItem>
                  <SelectItem value="Letter">Letter (8.5 × 11 in)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="file">Template File</Label>
              <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                <input
                  id="file"
                  type="file"
                  accept=".html,.htm,.docx"
                  onChange={handleFileChange}
                  className="hidden"
                  required
                />
                <label
                  htmlFor="file"
                  className="cursor-pointer flex flex-col items-center gap-2"
                >
                  <Upload className="h-8 w-8 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">
                      {formData.file
                        ? formData.file.name
                        : "Click to upload DOCX or HTML file"}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Supports .docx, .html and .htm files
                    </p>
                  </div>
                </label>
              </div>
            </div>

            <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <div className="flex items-start gap-2">
                <AlertCircle className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                <div className="text-sm">
                  <p className="font-medium text-blue-800 dark:text-blue-200 mb-1">
                    Template Guidelines:
                  </p>
                  <ul className="text-blue-700 dark:text-blue-300 space-y-1 list-disc list-inside">
                    <li>
                      Upload DOCX files directly or save Word documents as
                      &quot;Web Page, Filtered&quot; (.html)
                    </li>
                    <li>
                      Use placeholders like [name], [date], [address] for
                      dynamic content
                    </li>
                    <li>DOCX files will be automatically converted to HTML</li>
                    <li>
                      The system will automatically clean Word-specific
                      formatting
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="flex gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
                disabled={isUploading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isUploading} className="flex-1">
                {isConverting
                  ? "Converting DOCX..."
                  : isUploading
                  ? "Uploading..."
                  : "Upload Template"}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
